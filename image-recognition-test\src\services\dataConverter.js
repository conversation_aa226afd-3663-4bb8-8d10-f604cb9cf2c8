/**
 * 数据转换引擎
 * 将TextIn API返回的数据转换为XPrinter数据格式
 */

// XPrinter标签类型常量
const ELEMENT_TYPES = {
  CANVAS: '3',           // 画布
  TEXT: '1',             // 文本
  BAR_CODE: '2',         // 一维码，条形码
  LINE: '4',             // 线条
  LOGO: '5',             // logo
  PICTURE: '6',          // 图片
  QR_CODE: '7',          // QR Code
  CIRCULAR: '8',         // 圆形
  TIME: '9',             // 时间
  TABLE: '10',           // 表格
  RECTANGLE: '11'        // 矩形
};

class DataConverter {
  constructor() {
    this.canvasWidth = 0;
    this.canvasHeight = 0;
    this.dpi = 144; // 默认DPI
  }

  /**
   * 转换TextIn数据为XPrinter格式
   * @param {Object} textinData - TextIn API返回的数据
   * @returns {Array} XPrinter格式的数据数组
   */
  convertToXPrinter(textinData) {
    try {
      console.log('开始转换TextIn数据:', textinData);
      
      if (!textinData.result || !textinData.result.pages) {
        throw new Error('TextIn数据格式不正确');
      }

      const result = [];
      const pages = textinData.result.pages;

      // 处理每一页
      pages.forEach((page, pageIndex) => {
        console.log(`处理第${pageIndex + 1}页:`, page);
        
        // 设置画布尺寸
        if (page.width && page.height) {
          this.canvasWidth = page.width;
          this.canvasHeight = page.height;
        }

        // 创建画布元素
        if (pageIndex === 0) {
          const canvas = this.createCanvasElement();
          result.push(canvas);
        }

        // 处理页面内容
        if (page.content && Array.isArray(page.content)) {
          page.content.forEach(item => {
            const convertedItem = this.convertContentItem(item);
            if (convertedItem) {
              result.push(convertedItem);
            }
          });
        }

        // 处理结构化数据
        if (page.structured && Array.isArray(page.structured)) {
          page.structured.forEach(item => {
            const convertedItems = this.convertStructuredItem(item, page.content);
            if (convertedItems && convertedItems.length > 0) {
              result.push(...convertedItems);
            }
          });
        }
      });

      console.log('转换完成，结果:', result);
      return result;
    } catch (error) {
      console.error('数据转换失败:', error);
      throw new Error(`数据转换失败: ${error.message}`);
    }
  }

  /**
   * 创建画布元素
   * @returns {Object} 画布元素
   */
  createCanvasElement() {
    return {
      elementType: ELEMENT_TYPES.CANVAS,
      os: 'web',
      versionCode: 0,
      cableLabelDirection: 2,
      cableLabelLength: 0,
      templateBg: ""
    };
  }

  /**
   * 转换内容项
   * @param {Object} item - TextIn内容项
   * @returns {Object|null} 转换后的XPrinter元素
   */
  convertContentItem(item) {
    switch (item.type) {
      case 'line':
        return this.convertTextLine(item);
      case 'image':
        return this.convertImage(item);
      default:
        console.warn('未知的内容类型:', item.type);
        return null;
    }
  }

  /**
   * 转换结构化项
   * @param {Object} item - TextIn结构化项
   * @param {Array} contentItems - 页面内容项数组
   * @returns {Array} 转换后的XPrinter元素数组
   */
  convertStructuredItem(item, contentItems = []) {
    switch (item.type) {
      case 'textblock':
        return this.convertTextBlock(item, contentItems);
      case 'table':
        return [this.convertTable(item)];
      case 'image':
        return [this.convertImageBlock(item, contentItems)];
      default:
        console.warn('未知的结构化类型:', item.type);
        return [];
    }
  }

  /**
   * 转换文本行
   * @param {Object} textLine - TextIn文本行数据
   * @returns {Object} XPrinter文本元素
   */
  convertTextLine(textLine) {
    const bounds = this.calculateBounds(textLine.pos);

    return {
      elementType: 1, // 数字类型
      x: bounds.x.toString(),
      y: bounds.y.toString(),
      width: bounds.width.toString(),
      height: bounds.height.toString(),
      rotationAngle: (textLine.angle || 0).toString(),
      content: textLine.text || '',
      textSize: '12.0',
      hAlignment: '1',
      bold: 'false',
      italic: 'false',
      underline: 'false',
      strikethrough: 'false',
      lockLocation: 'false',
      takePrint: 'true',
      mirrorImage: 'false',
      wordSpace: '0.0',
      linesSpace: '0.0',
      fontType: '-2',
      blackWhiteReflection: 'false',
      automaticHeightCalculation: 'true',
      lineWrap: 'true',
      flipX: 'false',
      // XPrinter必需字段
      inputDataType: '1',
      transmutationType: 1,
      transmutationValue: '1',
      transmutationCount: '0',
      transmutationNegativeNumbers: 'false',
      excelPos: -1,
      bindKey: '',
      controlType: '3',
      textArrangementType: 0,
      arcAngle: 180,
      prefix: '',
      suffix: '',
      showKeyName: false
    };
  }

  /**
   * 转换图像
   * @param {Object} image - TextIn图像数据
   * @returns {Object} XPrinter元素
   */
  convertImage(image) {
    const bounds = this.calculateBounds(image.pos);

    // 根据子类型确定元素类型和属性
    switch (image.sub_type) {
      case 'qrcode':
        return {
          elementType: 7, // QR_CODE
          x: bounds.x.toString(),
          y: bounds.y.toString(),
          width: bounds.width.toString(),
          height: bounds.height.toString(),
          rotationAngle: '0',
          lockLocation: 'false',
          takePrint: 'true',
          mirrorImage: 'false',
          codeType: 'QR_CODE',
          whiteMargin: '0',
          errorCorrectionLevel: 'M',
          content: image.text || '',
          inputDataType: '1',
          prefix: '',
          suffix: '',
          transmutationValue: '1',
          transmutationCount: '0',
          transmutationType: 1,
          transmutationNegativeNumbers: 'false',
          excelPos: -1,
          bindKey: '',
          showKeyName: false
        };
      case 'barcode':
        return {
          elementType: 2, // BAR_CODE
          x: bounds.x.toString(),
          y: bounds.y.toString(),
          width: bounds.width.toString(),
          height: bounds.height.toString(),
          rotationAngle: '0',
          lockLocation: 'false',
          takePrint: 'true',
          mirrorImage: 'false',
          barcodeType: '4', // CODE128
          showText: '3',
          textAlignment: 1,
          content: image.text || '',
          horizontalAlignment: 'true',
          inputDataType: '1',
          transmutationValue: '1',
          transmutationCount: '0',
          transmutationType: 1,
          transmutationNegativeNumbers: 'false',
          excelPos: -1,
          bindKey: '',
          showKeyName: false,
          textSize: '11.0',
          fontType: '-2',
          bold: 'false',
          italic: 'false',
          underline: 'false',
          strikethrough: 'false',
          controlType: '2'
        };
      case 'stamp':
      case 'chart':
      default:
        return {
          elementType: 6, // PICTURE
          x: bounds.x.toString(),
          y: bounds.y.toString(),
          width: bounds.width.toString(),
          height: bounds.height.toString(),
          rotationAngle: '0',
          lockLocation: 'false',
          takePrint: 'true',
          mirrorImage: 'false',
          content: image.data?.base64 || '',
          colorMode: '0',
          grayValue: '128',
          tile: 'false',
          blackWhiteReflection: 'false'
        };
    }
  }

  /**
   * 转换文本块
   * @param {Object} textBlock - TextIn文本块数据
   * @param {Array} contentItems - 内容项数组
   * @returns {Array} XPrinter文本元素数组
   */
  convertTextBlock(textBlock, contentItems) {
    const result = [];
    
    if (textBlock.content && Array.isArray(textBlock.content)) {
      textBlock.content.forEach(contentId => {
        const contentItem = contentItems.find(item => item.id === contentId);
        if (contentItem && contentItem.type === 'line') {
          const textElement = this.convertTextLine(contentItem);
          result.push(textElement);
        }
      });
    }

    return result;
  }

  /**
   * 转换表格
   * @param {Object} table - TextIn表格数据
   * @returns {Object} XPrinter表格元素
   */
  convertTable(table) {
    const bounds = this.calculateBounds(table.pos);

    // 转换单元格
    const cells = [];
    if (table.cells && Array.isArray(table.cells)) {
      table.cells.forEach(cell => {
        cells.push({
          row: cell.row.toString(),
          col: cell.col.toString(),
          rowSpan: (cell.row_span || 1).toString(),
          colSpan: (cell.col_span || 1).toString(),
          content: cell.text || '',
          hAlignment: '1',
          textSize: '12',
          bold: 'false',
          italic: 'false',
          underline: 'false',
          strikethrough: 'false',
          automaticHeightCalculation: true,
          lineWrap: 'true',
          horizontalAlignment: true,
          blackWhiteReflection: false,
          fontType: '0',
          wordSpace: '0',
          linesSpace: '0'
        });
      });
    }

    return {
      elementType: 10, // TABLE
      x: bounds.x.toString(),
      y: bounds.y.toString(),
      width: bounds.width.toString(),
      height: bounds.height.toString(),
      rotationAngle: '0',
      lockLocation: 'false',
      takePrint: 'true',
      mirrorImage: 'false',
      rowHeights: table.rows_height || [],
      columnWidths: table.columns_width || [],
      borderWidth: '1',
      cells: cells
    };
  }

  /**
   * 转换图像块
   * @param {Object} imageBlock - TextIn图像块数据
   * @param {Array} contentItems - 内容项数组
   * @returns {Object} XPrinter图片元素
   */
  convertImageBlock(imageBlock, contentItems) {
    const bounds = this.calculateBounds(imageBlock.pos);

    // 查找对应的图像内容
    let imageContent = '';
    if (imageBlock.content && Array.isArray(imageBlock.content)) {
      const imageItem = contentItems.find(item =>
        imageBlock.content.includes(item.id) && item.type === 'image'
      );
      if (imageItem && imageItem.data) {
        imageContent = imageItem.data.base64 || '';
      }
    }

    return {
      elementType: 6, // PICTURE
      x: bounds.x.toString(),
      y: bounds.y.toString(),
      width: bounds.width.toString(),
      height: bounds.height.toString(),
      rotationAngle: '0',
      lockLocation: 'false',
      takePrint: 'true',
      mirrorImage: 'false',
      content: imageContent,
      colorMode: '0',
      grayValue: '128',
      tile: 'false',
      blackWhiteReflection: 'false'
    };
  }

  /**
   * 计算边界框
   * @param {Array} pos - 位置数组 [x1,y1,x2,y2,x3,y3,x4,y4]
   * @returns {Object} 边界框 {x, y, width, height}
   */
  calculateBounds(pos) {
    if (!pos || pos.length !== 8) {
      return { x: 0, y: 0, width: 100, height: 20 };
    }

    const xs = [pos[0], pos[2], pos[4], pos[6]];
    const ys = [pos[1], pos[3], pos[5], pos[7]];

    const minX = Math.min(...xs);
    const maxX = Math.max(...xs);
    const minY = Math.min(...ys);
    const maxY = Math.max(...ys);

    return {
      x: Math.round(minX),
      y: Math.round(minY),
      width: Math.round(maxX - minX),
      height: Math.round(maxY - minY)
    };
  }
}

// 创建单例实例
const dataConverter = new DataConverter();

export default dataConverter;
